"""
Main CLI interface for 200Model8CLI

Provides the command-line interface using Click framework with rich terminal UI.
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional, List

import click
import structlog
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn

from .core.config import Config
from .core.api import OpenRouterClient
from .core.models import ModelManager
from .core.session import SessionManager
from .tools.base import ToolRegistry
from .tools.file_ops import FileOperations
from .tools.web_tools import WebTools
from .tools.git_tools import GitTools
from .tools.system_tools import SystemTools
from .tools.code_tools import CodeTools
from .ui.interactive import InteractiveMode
from .ui.formatting import RichFormatter

# Initialize console and logger
console = Console()
logger = structlog.get_logger(__name__)


def setup_logging(level: str = "INFO"):
    """Setup structured logging"""
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


@click.group(invoke_without_command=True)
@click.option('--config', '-c', type=click.Path(), help='Configuration file path')
@click.option('--model', '-m', help='Model to use for this session')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.option('--debug', is_flag=True, help='Enable debug logging')
@click.pass_context
def main(ctx, config: Optional[str], model: Optional[str], verbose: bool, debug: bool):
    """200Model8CLI - OpenRouter CLI Agent
    
    A sophisticated command-line interface tool that uses OpenRouter's API 
    to access multiple AI models with comprehensive tool calling capabilities.
    """
    # Setup logging
    log_level = "DEBUG" if debug else ("INFO" if verbose else "WARNING")
    setup_logging(log_level)
    
    # Load configuration
    try:
        config_path = Path(config) if config else None
        app_config = Config(config_path)
        
        # Override model if specified
        if model:
            app_config.models.default = model
        
        ctx.ensure_object(dict)
        ctx.obj['config'] = app_config
        
    except Exception as e:
        console.print(f"[red]Configuration error: {e}[/red]")
        sys.exit(1)
    
    # If no subcommand, start interactive mode
    if ctx.invoked_subcommand is None:
        asyncio.run(start_interactive_mode(app_config))


@main.command()
@click.pass_context
def interactive(ctx):
    """Start interactive mode"""
    config = ctx.obj['config']
    asyncio.run(start_interactive_mode(config))


@main.command()
@click.argument('path', type=click.Path(exists=True))
@click.option('--encoding', default='utf-8', help='File encoding')
@click.pass_context
def read(ctx, path: str, encoding: str):
    """Read a file"""
    config = ctx.obj['config']
    asyncio.run(read_file_command(config, path, encoding))


@main.command()
@click.argument('path', type=click.Path())
@click.argument('content')
@click.option('--encoding', default='utf-8', help='File encoding')
@click.option('--backup/--no-backup', default=True, help='Create backup')
@click.pass_context
def write(ctx, path: str, content: str, encoding: str, backup: bool):
    """Write content to a file"""
    config = ctx.obj['config']
    asyncio.run(write_file_command(config, path, content, encoding, backup))


@main.command()
@click.argument('path', type=click.Path(exists=True))
@click.argument('changes')
@click.option('--backup/--no-backup', default=True, help='Create backup')
@click.pass_context
def edit(ctx, path: str, changes: str, backup: bool):
    """Edit a file with AI assistance"""
    config = ctx.obj['config']
    asyncio.run(edit_file_command(config, path, changes, backup))


@main.command()
@click.argument('path')
@click.option('--parents', is_flag=True, help='Create parent directories as needed')
@click.pass_context
def mkdir(ctx, path: str, parents: bool):
    """Create a directory"""
    import os
    try:
        if parents:
            os.makedirs(path, exist_ok=True)
        else:
            os.mkdir(path)
        click.echo(f"✅ Directory created: {path}")
    except FileExistsError:
        click.echo(f"❌ Directory already exists: {path}")
    except Exception as e:
        click.echo(f"❌ Failed to create directory: {e}")


@main.command()
@click.option('--directory', '-d', default='.', help='Directory to search')
@click.option('--pattern', '-p', help='File name pattern')
@click.option('--content', '-c', help='Content to search for')
@click.option('--recursive/--no-recursive', default=True, help='Recursive search')
@click.option('--case-sensitive', is_flag=True, help='Case sensitive search')
@click.option('--max-results', default=100, help='Maximum results')
@click.pass_context
def search(ctx, directory: str, pattern: Optional[str], content: Optional[str], 
          recursive: bool, case_sensitive: bool, max_results: int):
    """Search for files and content"""
    config = ctx.obj['config']
    asyncio.run(search_files_command(
        config, directory, pattern, content, recursive, case_sensitive, max_results
    ))


@main.command()
@click.pass_context
def models(ctx):
    """List available models"""
    config = ctx.obj['config']
    asyncio.run(list_models_command(config))


@main.command()
@click.argument('model_name')
@click.pass_context
def use_model(ctx, model_name: str):
    """Switch to a different model"""
    config = ctx.obj['config']
    asyncio.run(switch_model_command(config, model_name))


@main.command()
@click.pass_context
def sessions(ctx):
    """List conversation sessions"""
    config = ctx.obj['config']
    asyncio.run(list_sessions_command(config))


@main.command()
@click.pass_context
def config_info(ctx):
    """Show configuration information"""
    config = ctx.obj['config']
    show_config_info(config)


@main.command()
@click.argument('api_key')
@click.pass_context
def set_api_key(ctx, api_key: str):
    """Set and save OpenRouter API key"""
    config = ctx.obj['config']
    asyncio.run(set_api_key_command(config, api_key))


@main.command()
@click.argument('code_or_file')
@click.option('--save', help='Save code to file before running')
@click.option('--file', is_flag=True, help='Treat argument as a file path')
@click.pass_context
def run_python(ctx, code_or_file: str, save: str = None, file: bool = False):
    """Run Python code directly or execute a Python file"""
    asyncio.run(run_python_command(code_or_file, save, file))


@main.command()
@click.argument('request', nargs=-1, required=True)
@click.pass_context
def ask(ctx, request):
    """Ask AI to do something in natural language"""
    config = ctx.obj['config']
    request_text = ' '.join(request)
    asyncio.run(ask_ai_command(config, request_text))


@main.command()
@click.option('--list', 'list_models', is_flag=True, help='List all working free models')
@click.pass_context
def switch(ctx, list_models):
    """Switch to next available free model automatically"""
    config = ctx.obj['config']
    asyncio.run(auto_switch_model_command(config, list_models))


@main.command()
@click.argument('query')
@click.pass_context
def search(ctx, query):
    """Quick web search"""
    config = ctx.obj['config']
    asyncio.run(quick_search_command(config, query))


@main.command()
@click.argument('command')
@click.pass_context
def cmd(ctx, command):
    """Execute a terminal command"""
    config = ctx.obj['config']
    asyncio.run(quick_command_execute(config, command))


@main.command()
@click.pass_context
def version(ctx):
    """Show version information"""
    show_version_info()


# Command implementations

async def start_interactive_mode(config: Config):
    """Start interactive mode"""
    try:
        console.print(Panel.fit(
            "[bold blue]200Model8CLI[/bold blue]\n"
            "OpenRouter CLI Agent\n"
            f"Default Model: [green]{config.default_model}[/green]",
            title="Welcome"
        ))
        
        # Initialize components
        async with OpenRouterClient(config) as api_client:
            model_manager = ModelManager(config, api_client)
            session_manager = SessionManager(config)
            tool_registry = ToolRegistry(config)
            
            # Register all tools
            file_ops = FileOperations(config)
            for tool in file_ops.get_tools():
                tool_registry.register_tool(tool)

            if config.tools.web_search_enabled:
                web_tools = WebTools(config)
                for tool in web_tools.get_tools():
                    tool_registry.register_tool(tool)

            if config.tools.git_operations_enabled:
                git_tools = GitTools(config)
                for tool in git_tools.get_tools():
                    tool_registry.register_tool(tool)

            if config.tools.system_operations_enabled:
                system_tools = SystemTools(config)
                for tool in system_tools.get_tools():
                    tool_registry.register_tool(tool)

            if config.tools.code_analysis_enabled:
                code_tools = CodeTools(config)
                for tool in code_tools.get_tools():
                    tool_registry.register_tool(tool)
            
            # Initialize model manager
            await model_manager.initialize()
            
            # Start interactive mode
            interactive_mode = InteractiveMode(
                config, api_client, model_manager, session_manager, tool_registry
            )
            
            await interactive_mode.start()
            
    except KeyboardInterrupt:
        console.print("\n[yellow]Goodbye![/yellow]")
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        logger.error("Interactive mode failed", error=str(e))


async def read_file_command(config: Config, path: str, encoding: str):
    """Read file command"""
    try:
        tool_registry = ToolRegistry(config)
        file_ops = FileOperations(config)
        
        for tool in file_ops.get_tools():
            tool_registry.register_tool(tool)
        
        result = await tool_registry.execute_tool("read_file", path=path, encoding=encoding)
        
        if result.success:
            file_data = result.result
            console.print(Panel(
                file_data['content'],
                title=f"[green]{file_data['path']}[/green]",
                subtitle=f"Size: {file_data['size_formatted']} | Language: {file_data['language']} | Lines: {file_data['line_count']}"
            ))
        else:
            console.print(f"[red]Error: {result.error}[/red]")
            
    except Exception as e:
        console.print(f"[red]Command failed: {e}[/red]")


async def write_file_command(config: Config, path: str, content: str, encoding: str, backup: bool):
    """Write file command"""
    try:
        tool_registry = ToolRegistry(config)
        file_ops = FileOperations(config)
        
        for tool in file_ops.get_tools():
            tool_registry.register_tool(tool)
        
        result = await tool_registry.execute_tool(
            "write_file", 
            path=path, 
            content=content, 
            encoding=encoding, 
            create_backup=backup
        )
        
        if result.success:
            file_data = result.result
            console.print(f"[green]✓[/green] File written: {file_data['path']}")
            console.print(f"  Size: {file_data['size_formatted']}")
            if file_data['backup_created']:
                console.print(f"  Backup: {file_data['backup_path']}")
        else:
            console.print(f"[red]Error: {result.error}[/red]")
            
    except Exception as e:
        console.print(f"[red]Command failed: {e}[/red]")


async def edit_file_command(config: Config, path: str, changes: str, backup: bool):
    """Edit file command"""
    try:
        tool_registry = ToolRegistry(config)
        file_ops = FileOperations(config)
        
        for tool in file_ops.get_tools():
            tool_registry.register_tool(tool)
        
        result = await tool_registry.execute_tool(
            "edit_file", 
            path=path, 
            changes=changes, 
            create_backup=backup
        )
        
        if result.success:
            file_data = result.result
            console.print(f"[green]✓[/green] File edited: {file_data['path']}")
            console.print(f"  Changes: {file_data['changes_applied']}")
            console.print(f"  Size: {file_data['size_formatted']}")
            if file_data['backup_created']:
                console.print(f"  Backup: {file_data['backup_path']}")
        else:
            console.print(f"[red]Error: {result.error}[/red]")
            
    except Exception as e:
        console.print(f"[red]Command failed: {e}[/red]")


async def search_files_command(
    config: Config, 
    directory: str, 
    pattern: Optional[str], 
    content: Optional[str],
    recursive: bool, 
    case_sensitive: bool, 
    max_results: int
):
    """Search files command"""
    try:
        tool_registry = ToolRegistry(config)
        file_ops = FileOperations(config)
        
        for tool in file_ops.get_tools():
            tool_registry.register_tool(tool)
        
        result = await tool_registry.execute_tool(
            "search_files",
            directory=directory,
            pattern=pattern,
            content=content,
            recursive=recursive,
            case_sensitive=case_sensitive,
            max_results=max_results
        )
        
        if result.success:
            search_data = result.result
            results = search_data['results']
            
            if not results:
                console.print("[yellow]No results found[/yellow]")
                return
            
            table = Table(title=f"Search Results ({len(results)} found)")
            table.add_column("Type", style="cyan")
            table.add_column("Path", style="green")
            table.add_column("Size", justify="right")
            table.add_column("Language", style="blue")
            table.add_column("Details", style="dim")
            
            for item in results:
                details = ""
                if item['type'] == 'content_match':
                    details = f"{item['total_matches']} matches"
                
                table.add_row(
                    item['type'].replace('_', ' ').title(),
                    item['path'],
                    item['size_formatted'],
                    item['language'],
                    details
                )
            
            console.print(table)
        else:
            console.print(f"[red]Error: {result.error}[/red]")
            
    except Exception as e:
        console.print(f"[red]Command failed: {e}[/red]")


async def list_models_command(config: Config):
    """List available models"""
    try:
        async with OpenRouterClient(config) as api_client:
            model_manager = ModelManager(config, api_client)
            await model_manager.initialize()
            
            models = model_manager.get_available_models()
            
            table = Table(title="Available Models")
            table.add_column("Model ID", style="green")
            table.add_column("Name", style="blue")
            table.add_column("Context", justify="right")
            table.add_column("Capabilities", style="cyan")
            table.add_column("Recommended For", style="dim")
            
            for model in models:
                capabilities = ", ".join([cap.value for cap in model.capabilities])
                recommended = ", ".join(model.recommended_for)
                
                table.add_row(
                    model.info.id,
                    model.info.name,
                    f"{model.info.context_length:,}",
                    capabilities[:50] + "..." if len(capabilities) > 50 else capabilities,
                    recommended[:50] + "..." if len(recommended) > 50 else recommended
                )
            
            console.print(table)
            
    except Exception as e:
        console.print(f"[red]Failed to list models: {e}[/red]")


async def list_sessions_command(config: Config):
    """List conversation sessions"""
    try:
        session_manager = SessionManager(config)
        sessions = session_manager.list_sessions()
        
        if not sessions:
            console.print("[yellow]No sessions found[/yellow]")
            return
        
        table = Table(title="Conversation Sessions")
        table.add_column("Name", style="green")
        table.add_column("Model", style="blue")
        table.add_column("Messages", justify="right")
        table.add_column("Tokens", justify="right")
        table.add_column("Created", style="dim")
        table.add_column("Updated", style="dim")
        
        for session in sessions:
            from datetime import datetime
            created = datetime.fromtimestamp(session.created_at).strftime("%Y-%m-%d %H:%M")
            updated = datetime.fromtimestamp(session.updated_at).strftime("%Y-%m-%d %H:%M")
            
            table.add_row(
                session.name,
                session.model,
                str(session.total_messages),
                f"{session.total_tokens:,}",
                created,
                updated
            )
        
        console.print(table)
        
    except Exception as e:
        console.print(f"[red]Failed to list sessions: {e}[/red]")


def show_config_info(config: Config):
    """Show configuration information"""
    table = Table(title="Configuration")
    table.add_column("Setting", style="cyan")
    table.add_column("Value", style="green")
    
    table.add_row("Config Path", str(config.config_path))
    table.add_row("Default Model", config.default_model)
    table.add_row("API Timeout", f"{config.api_timeout}s")
    table.add_row("Max Retries", str(config.max_retries))
    table.add_row("Streaming", str(config.ui.streaming))
    table.add_row("Syntax Highlighting", str(config.ui.syntax_highlighting))
    table.add_row("File Operations", str(config.tools.file_operations_enabled))
    table.add_row("Web Search", str(config.tools.web_search_enabled))
    table.add_row("Git Operations", str(config.tools.git_operations_enabled))
    
    console.print(table)


def show_version_info():
    """Show version information"""
    from . import __version__, __author__
    
    console.print(Panel.fit(
        f"[bold blue]200Model8CLI[/bold blue]\n"
        f"Version: [green]{__version__}[/green]\n"
        f"Author: [cyan]{__author__}[/cyan]\n"
        f"Python: [yellow]{sys.version.split()[0]}[/yellow]",
        title="Version Information"
    ))


async def switch_model_command(config: Config, model_name: str):
    """Switch to a different model"""
    try:
        # Check if model is available
        if model_name not in config.models.available:
            console.print(f"[red]❌ Model '{model_name}' not available[/red]")
            console.print(f"[yellow]Available models:[/yellow]")
            for model in config.models.available:
                console.print(f"  - {model}")
            return

        # Update config
        config.models.default = model_name

        # Save to config file (if exists)
        config_path = Path.home() / ".200model8cli" / "config.yaml"
        if config_path.exists():
            import yaml
            with open(config_path, 'r') as f:
                config_data = yaml.safe_load(f)

            config_data['models']['default'] = model_name

            with open(config_path, 'w') as f:
                yaml.dump(config_data, f, default_flow_style=False)

        console.print(f"[green]✅ Switched to model: {model_name}[/green]")

    except Exception as e:
        console.print(f"[red]❌ Error switching model: {e}[/red]")


async def set_api_key_command(config: Config, api_key: str):
    """Set and save API key"""
    try:
        # Validate API key format
        if not api_key.startswith("sk-or-v1-"):
            console.print(f"[red]❌ Invalid API key format. Should start with 'sk-or-v1-'[/red]")
            return

        # Set environment variable to skip validation during save
        os.environ["SKIP_API_KEY_VALIDATION"] = "true"

        # Update config
        config.api.openrouter_key = api_key

        # Save to config file
        config._save_api_key_to_config(api_key)

        # Remove the skip flag
        if "SKIP_API_KEY_VALIDATION" in os.environ:
            del os.environ["SKIP_API_KEY_VALIDATION"]

        console.print(f"[green]✅ API key saved successfully![/green]")
        console.print(f"[green]✅ You can now use 200model8cli without setting the API key each time[/green]")

    except Exception as e:
        console.print(f"[red]❌ Error saving API key: {e}[/red]")


async def run_python_command(code_or_file: str, save_file: str = None, is_file: bool = False):
    """Run Python code directly or execute a Python file"""
    try:
        import tempfile
        import subprocess
        from pathlib import Path

        # Check if it's a file path (either explicitly marked or has .py extension)
        if is_file or code_or_file.endswith('.py'):
            # Running an existing file
            file_path = Path(code_or_file)
            if not file_path.exists():
                console.print(f"[red]❌ File not found: {code_or_file}[/red]")
                return

            file_to_run = str(file_path)
            console.print(f"[blue]🐍 Running Python file: {file_to_run}[/blue]")

        else:
            # Running code as string
            if save_file:
                with open(save_file, 'w') as f:
                    f.write(code_or_file)
                console.print(f"[green]✅ Code saved to {save_file}[/green]")
                file_to_run = save_file
            else:
                # Create temporary file
                with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                    f.write(code_or_file)
                    file_to_run = f.name

            console.print(f"[blue]🐍 Running Python code...[/blue]")

        # Execute the Python file
        result = subprocess.run(
            ["python", file_to_run],
            capture_output=True,
            text=True,
            timeout=30
        )

        # Show results
        if result.stdout:
            console.print(f"[green]📤 Output:[/green]")
            console.print(result.stdout)

        if result.stderr:
            console.print(f"[red]❌ Error:[/red]")
            console.print(result.stderr)

        console.print(f"[blue]✅ Exit code: {result.returncode}[/blue]")

        # Clean up temp file if not saved and not an existing file
        if not save_file and not (is_file or code_or_file.endswith('.py')):
            import os
            os.unlink(file_to_run)

    except Exception as e:
        console.print(f"[red]❌ Error running Python: {e}[/red]")


async def ask_ai_command(config: Config, request: str):
    """Process a natural language request with AI"""
    try:
        from model8cli.ui.interactive import InteractiveMode
        from model8cli.ui.formatting import RichFormatter
        from model8cli.core.session import SessionManager
        from model8cli.core.api import OpenRouterClient
        from model8cli.core.models import ModelManager
        from model8cli.tools.base import ToolRegistry
        from model8cli.tools.file_ops import FileOperations
        from model8cli.tools.web_tools import WebTools
        from model8cli.tools.git_tools import GitTools
        from model8cli.tools.system_tools import SystemTools
        from model8cli.tools.code_tools import CodeTools

        # Create all required components
        api_client = OpenRouterClient(config)
        model_manager = ModelManager(config, api_client)
        session_manager = SessionManager(config)
        tool_registry = ToolRegistry(config)

        # Register ALL tools
        console.print(f"[blue]🔧 Registering tools...[/blue]")

        # File operations
        file_ops = FileOperations(config)
        for tool in file_ops.get_tools():
            tool_registry.register_tool(tool)

        # Web tools (for search)
        web_tools = WebTools(config)
        for tool in web_tools.get_tools():
            tool_registry.register_tool(tool)

        # Git tools
        git_tools = GitTools(config)
        for tool in git_tools.get_tools():
            tool_registry.register_tool(tool)

        # System tools (for terminal commands)
        system_tools = SystemTools(config)
        for tool in system_tools.get_tools():
            tool_registry.register_tool(tool)

        # Code tools
        code_tools = CodeTools(config)
        for tool in code_tools.get_tools():
            tool_registry.register_tool(tool)

        console.print(f"[green]✅ {len(tool_registry.tools)} tools registered[/green]")

        # Create interactive mode
        interactive = InteractiveMode(
            config=config,
            api_client=api_client,
            model_manager=model_manager,
            session_manager=session_manager,
            tool_registry=tool_registry
        )

        console.print(f"[blue]🤖 Processing request: {request}[/blue]")

        # Process the request
        await interactive._process_user_message(request)

    except Exception as e:
        console.print(f"[red]❌ Error processing request: {e}[/red]")


async def auto_switch_model_command(config: Config, list_models: bool = False):
    """Automatically switch to next available free model"""
    try:
        from model8cli.core.api import OpenRouterClient
        from model8cli.core.models import ModelManager

        # Create API client and model manager to check availability
        api_client = OpenRouterClient(config)
        model_manager = ModelManager(config, api_client)

        console.print("[blue]🔍 Checking model availability...[/blue]")

        # Get actually available models
        try:
            await model_manager.load_models()
            available_models = list(config.models.available)
        except Exception as e:
            console.print(f"[yellow]⚠️ Could not fetch models: {e}[/yellow]")
            # Fallback to basic list
            available_models = [
                "deepseek/deepseek-r1:free",
                "deepseek/deepseek-chat-v3-0324:free",
                "meta-llama/llama-3.2-3b-instruct:free",
                "microsoft/phi-3-mini-4k-instruct:free"
            ]

        # Filter for free models only
        free_models = [model for model in available_models if ":free" in model]

        if not free_models:
            console.print("[red]❌ No free models available[/red]")
            return

        if list_models:
            console.print("[blue]🔄 Available Free Models:[/blue]")
            for i, model in enumerate(free_models, 1):
                current = "← Current" if model == config.models.default else ""
                console.print(f"  {i}. {model} {current}")
            return

        # Find current model index
        current_model = config.models.default
        current_index = -1

        try:
            current_index = free_models.index(current_model)
        except ValueError:
            console.print(f"[yellow]⚠️ Current model '{current_model}' not available[/yellow]")

        # Switch to next model
        next_index = (current_index + 1) % len(free_models)
        next_model = free_models[next_index]

        # Validate the model is actually available
        if next_model not in available_models:
            console.print(f"[red]❌ Model {next_model} not available, trying first available...[/red]")
            next_model = free_models[0]

        # Update config
        config.models.default = next_model

        # Save to config file
        config_path = Path.home() / ".200model8cli" / "config.yaml"
        if config_path.exists():
            import yaml
            with open(config_path, 'r') as f:
                config_data = yaml.safe_load(f) or {}

            if "models" not in config_data:
                config_data["models"] = {}
            config_data["models"]["default"] = next_model

            with open(config_path, 'w') as f:
                yaml.dump(config_data, f, default_flow_style=False)

        console.print(f"[green]✅ Switched to: {next_model}[/green]")
        console.print(f"[blue]💡 Try: 200model8cli[/blue]")

    except Exception as e:
        console.print(f"[red]❌ Error switching model: {e}[/red]")


async def quick_search_command(config: Config, query: str):
    """Quick web search without full AI interaction"""
    try:
        from model8cli.tools.web_tools import WebTools

        console.print(f"[blue]🔍 Searching for: {query}[/blue]")

        # Create web search tool
        web_search_tool = WebTools(config).get_tools()[0]  # Get the search tool
        result = await web_search_tool.execute(query, max_results=5)

        if result.success:
            console.print(f"[green]✅ Search Results:[/green]")
            console.print(result.result)
        else:
            console.print(f"[red]❌ Search failed: {result.error}[/red]")

    except Exception as e:
        console.print(f"[red]❌ Error searching: {e}[/red]")


async def quick_command_execute(config: Config, command: str):
    """Execute a terminal command directly"""
    try:
        from model8cli.tools.system_tools import SystemTools

        console.print(f"[blue]💻 Executing: {command}[/blue]")

        # Get the command execution tool (first tool in SystemTools)
        system_tools = SystemTools(config)
        command_tool = system_tools.get_tools()[0]  # Get the execute_command tool
        result = await command_tool.execute(command)

        if result.success:
            console.print(f"[green]✅ Command executed successfully[/green]")
            if result.result.get("stdout"):
                console.print(f"[blue]📤 Output:[/blue]")
                console.print(result.result["stdout"])
            if result.result.get("stderr"):
                console.print(f"[yellow]⚠️ Warnings:[/yellow]")
                console.print(result.result["stderr"])
        else:
            console.print(f"[red]❌ Command failed: {result.error}[/red]")

    except Exception as e:
        console.print(f"[red]❌ Error executing command: {e}[/red]")


if __name__ == '__main__':
    main()
